import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:signature/signature.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class SignaturePage extends StatefulWidget {
  final num? questionId;
  final num? taskId;
  final num? formId;
  final String? title;

  const SignaturePage({
    super.key,
    this.questionId,
    this.taskId,
    this.formId,
    this.title,
  });

  @override
  State<SignaturePage> createState() => _SignaturePageState();
}

class _SignaturePageState extends State<SignaturePage> {
  late SignatureController _signatureController;
  final TextEditingController _printNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _signatureController = SignatureController(
      penStrokeWidth: 2,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );
  }

  @override
  void dispose() {
    _signatureController.dispose();
    _printNameController.dispose();
    super.dispose();
  }

  void _clearSignature() {
    _signatureController.clear();
  }

  void _handleNotAvailable() {
    // Handle "Not available" action
    Navigator.pop(context);
  }

  void _handleSubmit() {
    // Handle signature submission
    // TODO: Implement signature submission logic
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: CustomAppBar(
          title: widget.title ?? 'Signature',
        ),
        body: Column(
          children: [
            // Main content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Question/Response table
                    _buildQuestionResponseTable(textTheme),

                    const Gap(20),

                    // Signature section
                    _buildSignatureSection(textTheme),

                    const Gap(20),

                    // Print name field
                    _buildPrintNameField(textTheme),

                    const Gap(100),
                  ],
                ),
              ),
            ),
          ],
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: _buildSubmitButton());
  }

  Widget _buildQuestionResponseTable(TextTheme textTheme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: AppColors.lightGrey1,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Question',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    'Response',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildQuestionRow('Did you went to the store?', 'Yes'),
                const Gap(12),
                _buildQuestionRow('Name the person you meet', 'dg'),
                const Gap(12),
                _buildQuestionRow('Please sign', 'ryh'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionRow(String question, String response) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            question,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const Gap(16),
        Expanded(
          child: Text(
            response,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignatureSection(TextTheme textTheme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Please sign below:',
            style: textTheme.montserratTitleExtraSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const Gap(16),

          // Action buttons
          _buildActionButtons(),
          const Gap(16),

          // Signature canvas
          Container(
            width: double.infinity,
            height: 150,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderBlack),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Signature(
                controller: _signatureController,
                backgroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: AppButton(
            text: 'Not available',
            color: AppColors.blackTint1,
            textColor: Colors.white,
            onPressed: _handleNotAvailable,
            height: 40,
          ),
        ),
        const Gap(12),
        Expanded(
          child: AppButton(
            text: 'Clear',
            color: AppColors.loginRed,
            textColor: Colors.white,
            onPressed: _clearSignature,
            height: 40,
          ),
        ),
      ],
    );
  }

  Widget _buildPrintNameField(TextTheme textTheme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Print name',
            style: textTheme.montserratTitleExtraSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const Gap(12),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderBlack),
            ),
            child: TextField(
              controller: _printNameController,
              style: textTheme.montserratParagraphSmall.copyWith(
                color: AppColors.black,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                hintText: 'Enter your name',
                hintStyle: textTheme.montserratParagraphSmall.copyWith(
                  color: AppColors.blackTint1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      width: double.infinity,
      child: AppButton(
        text: 'Submit signature',
        color: AppColors.primaryBlue,
        textColor: Colors.white,
        onPressed: _handleSubmit,
        height: 50,
      ),
    );
  }
}
