import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:signature/signature.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/signature_page/signature_page_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/signature_page/signature_page_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';

@RoutePage()
class SignaturePage extends StatefulWidget {
  final num? questionId;
  final num? taskId;
  final num? formId;
  final String? title;

  const SignaturePage({
    super.key,
    this.questionId,
    this.taskId,
    this.formId,
    this.title,
  });

  @override
  State<SignaturePage> createState() => _SignaturePageState();
}

class _SignaturePageState extends State<SignaturePage> {
  late SignatureController _signatureController;
  final TextEditingController _printNameController = TextEditingController();
  late SignaturePageCubit _cubit;

  @override
  void initState() {
    super.initState();
    _signatureController = SignatureController(
      penStrokeWidth: 2,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );
    _cubit = SignaturePageCubit();

    // Load form data if taskId and formId are provided
    if (widget.taskId != null && widget.formId != null) {
      _cubit.loadFormData(
        taskId: widget.taskId!.toInt(),
        formId: widget.formId!.toInt(),
      );
    }
  }

  @override
  void dispose() {
    _signatureController.dispose();
    _printNameController.dispose();
    _cubit.close();
    super.dispose();
  }

  void _clearSignature() {
    _signatureController.clear();
  }

  void _handleNotAvailable() {
    // Handle "Not available" action
    Navigator.pop(context);
  }

  Future<void> _handleSubmit() async {
    try {
      // Check if signature is empty
      if (_signatureController.isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'Please provide a signature before submitting',
        );
        return;
      }

      // Check if name is provided
      if (_printNameController.text.trim().isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'Please enter your name before submitting',
        );
        return;
      }

      // Export signature as image
      final signatureBytes = await _signatureController.toPngBytes();
      if (signatureBytes == null) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Failed to export signature',
          );
        }
        return;
      }

      // Save signature using cubit
      if (widget.taskId != null && widget.formId != null) {
        await _cubit.saveSignature(
          taskId: widget.taskId!.toInt(),
          formId: widget.formId!.toInt(),
          signatureBytes: signatureBytes,
          signedBy: _printNameController.text.trim(),
        );
      }
    } catch (e) {
      logger('Error submitting signature: $e');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to submit signature. Please try again.',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return BlocProvider.value(
      value: _cubit,
      child: BlocConsumer<SignaturePageCubit, SignaturePageState>(
        listener: (context, state) {
          if (state is SignaturePageError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          } else if (state is SignaturePageSaved) {
            SnackBarService.success(
              context: context,
              message: 'Signature saved successfully!',
            );
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: AppColors.lightGrey2,
            appBar: CustomAppBar(
              title: widget.title ?? 'Signature',
            ),
            body: Column(
              children: [
                // Main content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Question/Response table
                        _buildQuestionResponseTable(textTheme, state),

                        const Gap(20),

                        // Signature section
                        _buildSignatureSection(textTheme, state),

                        const Gap(20),

                        // Print name field
                        _buildPrintNameField(textTheme),

                        const Gap(100),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            floatingActionButton: _buildSubmitButton(state),
          );
        },
      ),
    );
  }

  Widget _buildQuestionResponseTable(
      TextTheme textTheme, SignaturePageState state) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: AppColors.lightGrey1,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Question',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    'Response',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (state is SignaturePageLoading)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (state is SignaturePageLoaded)
                  ..._buildQuestionAnswerRows(state.questionAnswers)
                else
                  const Text(
                    'Loading questions and answers...',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.blackTint1,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildQuestionAnswerRows(List<dynamic> questionAnswers) {
    if (questionAnswers.isEmpty) {
      return [
        const Text(
          'No questions and answers available for this form.',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.blackTint1,
          ),
        ),
      ];
    }

    final List<Widget> rows = [];
    for (int i = 0; i < questionAnswers.length; i++) {
      QuestionAnswer qa = questionAnswers[i];
      rows.add(_buildQuestionRow(
        qa.questionDescription ?? 'Question ${i + 1}',
        qa.measurementDescription ??
            qa.measurementValue?.toString() ??
            'No answer',
      ));
      if (i < questionAnswers.length - 1) {
        rows.add(const Gap(12));
      }
    }
    return rows;
  }

  Widget _buildQuestionRow(String question, String response) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            question,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const Gap(16),
        Expanded(
          child: Text(
            response,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignatureSection(TextTheme textTheme, SignaturePageState state) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Please sign below:',
            style: textTheme.montserratTitleExtraSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const Gap(16),

          // Action buttons
          _buildActionButtons(),
          const Gap(16),

          // Signature canvas
          Container(
            width: double.infinity,
            height: 150,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderBlack),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                children: [
                  // Show existing signature if available
                  if (state is SignaturePageLoaded &&
                      state.existingSignatureUrl != null)
                    Positioned.fill(
                      child: Image.file(
                        File(state.existingSignatureUrl!),
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Text(
                              'Error loading existing signature',
                              style: TextStyle(color: Colors.red, fontSize: 12),
                            ),
                          );
                        },
                      ),
                    ),
                  // Signature canvas (always present for new signatures)
                  Signature(
                    controller: _signatureController,
                    backgroundColor: (state is SignaturePageLoaded &&
                            state.existingSignatureUrl != null)
                        ? Colors.transparent
                        : Colors.white,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: AppButton(
            text: 'Not available',
            color: AppColors.blackTint1,
            textColor: Colors.white,
            onPressed: _handleNotAvailable,
            height: 40,
          ),
        ),
        const Gap(12),
        Expanded(
          child: AppButton(
            text: 'Clear',
            color: AppColors.loginRed,
            textColor: Colors.white,
            onPressed: _clearSignature,
            height: 40,
          ),
        ),
      ],
    );
  }

  Widget _buildPrintNameField(TextTheme textTheme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Print name',
            style: textTheme.montserratTitleExtraSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const Gap(12),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderBlack),
            ),
            child: TextField(
              controller: _printNameController,
              style: textTheme.montserratParagraphSmall.copyWith(
                color: AppColors.black,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                hintText: 'Enter your name',
                hintStyle: textTheme.montserratParagraphSmall.copyWith(
                  color: AppColors.blackTint1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(SignaturePageState state) {
    final isLoading = state is SignaturePageSaving;

    return Container(
      padding: const EdgeInsets.all(16),
      width: double.infinity,
      child: AppButton(
        text: isLoading ? 'Saving...' : 'Submit signature',
        color: isLoading ? AppColors.midGrey : AppColors.primaryBlue,
        textColor: Colors.white,
        onPressed: () {
          if (!isLoading) {
            _handleSubmit();
          }
        },
        height: 50,
      ),
    );
  }
}
