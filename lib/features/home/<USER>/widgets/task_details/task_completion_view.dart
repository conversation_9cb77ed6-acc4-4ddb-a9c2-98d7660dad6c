import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/segment_indicator.dart';

class TaskCompletionView extends StatelessWidget {
  final entities.TaskDetail task;
  final FormProgress? formProgress;

  const TaskCompletionView({
    super.key,
    required this.task,
    this.formProgress,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // Use formProgress if available, otherwise fallback to existing logic
    final max = formProgress?.totalVisible ?? task.forms?.length ?? 0;
    final completed =
        formProgress?.totalCompleted ?? task.ctFormsCompletedCnt ?? 0;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text('Task Completion', style: textTheme.montserratTitleXxsmall),
          const Gap(8),
          Row(
            children: [
              Text(
                (completed == 0 && max == 0)
                    ? '0%'
                    : '${((completed / max) * 100).toStringAsFixed(0)}%',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const Gap(8),
          LayoutBuilder(
            builder: (context, constraints) {
              final progressWidth = constraints.maxWidth;
              final progress =
                  (completed == 0 && max == 0) ? 0.0 : (completed / max);

              // Calculate position based on the width of the SegmentedProgressIndicator
              final position = progressWidth * progress;

              // Center the line (line width is 1.5)
              final adjustedPosition = position - 0.75;

              return Stack(
                clipBehavior: Clip.none,
                children: [
                  SegmentedProgressIndicator(
                    progress: progress,
                    totalWidth: progressWidth,
                    activeColor: AppColors.primaryBlue,
                    backgroundColor: Colors.grey.shade200,
                    dividerColor: Colors.black,
                    height: 10,
                    segments: 10,
                    borderRadius: 10,
                  ),
                  Positioned(
                    left: adjustedPosition,
                    top: -8,
                    bottom: 0,
                    child: Container(
                      width: 1.5,
                      color: Colors.black,
                    ),
                  ),
                ],
              );
            },
          ),
          const Gap(8),
          Text('$completed of $max forms',
              style: textTheme.montserratTableSmall),
        ],
      ),
    );
  }
}
