import 'package:auto_route/auto_route.dart';

// Force regeneration of app_router.gr.dart
import 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => const RouteType.cupertino();

  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: ResetPasswordRoute.page,
        ),
        AutoRoute(
          page: SplashRoute.page,
          initial: true,
        ),
        AutoRoute(
          page: LoginRoute.page,
        ),
        AutoRoute(
          page: HomeRoute.page,
          children: [
            AutoRoute(
              page: DashboardHolderRoute.page,
              initial: true,
              children: [
                AutoRoute(page: UnscheduledRoute.page),
                <PERSON>Route(page: ScheduleRoute.page),
                AutoRoute(page: NotificationsRoute.page),
                AutoRoute(page: TodayRoute.page),
                AutoRoute(page: JourneyMapRoute.page),
                AutoRoute(
                  page: TaskDetailsRoute.page,
                ),
                AutoRoute(
                  page: PosRoute.page,
                ),
                AutoRoute(
                  page: NotesRoute.page,
                ),
                <PERSON>Route(
                  page: StoreHistoryRoute.page,
                ),
                AutoRoute(
                  page: DashboardRoute.page,
                  initial: true,
                ),
                AutoRoute(page: StoreInfoRoute.page),
                AutoRoute(page: FormRoute.page),
                AutoRoute(page: QuestionRoute.page),
                AutoRoute(page: QPMDRoute.page),
                AutoRoute(page: FQPDRoute.page),
                AutoRoute(page: SubHeaderRoute.page),
                AutoRoute(page: SignatureRoute.page),
                AutoRoute(page: MPTRoute.page),
                AutoRoute(page: BarcodeScannerRoute.page),
              ],
            ),
            AutoRoute(page: AssistantRoute.page),
            AutoRoute(
              page: ProfileHolderRoute.page,
              children: [
                AutoRoute(
                  page: ProfileRoute.page,
                  initial: true,
                ),
                AutoRoute(page: EditProfileRoute.page),
                AutoRoute(page: AvailabilityRoute.page),
                AutoRoute(page: SkillsRoute.page),
                AutoRoute(page: LeaveRoute.page),
                AutoRoute(page: InductionRoute.page),
                AutoRoute(page: HistoryRoute.page),
              ],
            ),
            AutoRoute(
              page: MoreHolderRoute.page,
              children: [
                AutoRoute(
                  page: MoreRoute.page,
                  initial: true,
                ),
                AutoRoute(page: UsefulLinksRoute.page),
              ],
            ),
          ],
        ),

        // Removed DashboardRoute children to avoid duplicate UnscheduledRoute
        // UnscheduledRoute will only be accessible as a child of HomeRoute.
        // If you need nested navigation in Dashboard, handle it via HomeRoute's children.
        AutoRoute(
          page: NotificationsRoute.page,
        ),
        AutoRoute(
          page: WebBrowserRoute.page,
        ),
      ];
}
